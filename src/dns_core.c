#include "../include/common.h"

// 全局变量定义
DnsEntry localTable[MAX_LOCAL_ENTRIES];
int localTableCount = 0;
CacheEntry cache[MAX_CACHE_ENTRIES];
int cacheCount = 0;
IdMapping idTable[MAX_ID_ENTRIES];
int idTableCount = 0;
char domainBuffer[MAX_DOMAIN_LENGTH];

// 从DNS数据包中提取域名
int extract_domain(const char *packet, int length, char *domain)
{
    // 检查数据包长度是否足够
    if (length < sizeof(DnsHeader) + 1)
    {
        return DNS_INVALID_PACKET;
    }

    int offset = sizeof(DnsHeader); // 跳过DNS头部
    int domainPos = 0;

    // 解析域名（DNS标准格式：长度+字符串）
    while (offset < length && packet[offset] != 0)
    {
        int len = (unsigned char)packet[offset];
        // 检查长度是否合法
        if (len > 63 || offset + len + 1 >= length || domainPos + len + 1 >= MAX_DOMAIN_LENGTH)
        {
            return DNS_INVALID_PACKET;
        }
        offset++;

        // 复制域名段
        for (int i = 0; i < len; i++)
        {
            domain[domainPos++] = packet[offset++];
        }

        // 如果不是最后一段，添加点号分隔符
        if (packet[offset] != 0)
        {
            domain[domainPos++] = '.';
        }
    }
    domain[domainPos] = '\0';
    return offset + 1; // 返回域名结束后的偏移量
}

// 在本地域名映射表中查找域名
int search_local_table(const char *domain)
{
    for (int i = 0; i < localTableCount; i++)
    {
        if (localTable[i].domain && strcmp(localTable[i].domain, domain) == 0)
        {
            return i; // 返回找到的索引
        }
    }
    return NOT_FOUND; // 未找到
}

// 在DNS缓存中查找域名
int search_cache(const char *domain)
{
    for (int i = 0; i < cacheCount; i++)
    {
        // 检查域名匹配且TTL有效
        if (cache[i].domain && strcmp(cache[i].domain, domain) == 0 && cache[i].ttl > 0)
        {
            return i; // 返回找到的索引
        }
    }
    return NOT_FOUND; // 未找到或已过期
}

// 向DNS缓存中插入新条目
void insert_cache(const char *domain, const char *ip, int ttl)
{
    // 简单的缓存替换策略：如果满了就覆盖最后一个
    if (cacheCount >= MAX_CACHE_ENTRIES)
    {
        cacheCount = MAX_CACHE_ENTRIES - 1; // 简单替换策略，实际应实现 LRU
    }

    // 分配内存并复制数据
    cache[cacheCount].domain = platform_strdup(domain);
    cache[cacheCount].ip = platform_strdup(ip);
    cache[cacheCount].ttl = ttl;

    // 检查内存分配是否成功
    if (!cache[cacheCount].domain || !cache[cacheCount].ip)
    {
        free(cache[cacheCount].domain);
        free(cache[cacheCount].ip);
        return;
    }
    cacheCount++;
}

// 更新缓存中所有条目的TTL值
void update_cache_ttl(void)
{
    for (int i = 0; i < cacheCount; i++)
    {
        // 递减TTL
        if (cache[i].ttl > 0)
        {
            cache[i].ttl--;
        }

        // 清理过期条目
        if (cache[i].ttl <= 0 && cache[i].domain)
        {
            free(cache[i].domain);
            free(cache[i].ip);
            cache[i].domain = NULL;
            cache[i].ip = NULL;
        }
    }
}

// 生成新的查询ID并建立映射关系
uint16_t generate_new_id(uint16_t old_id, const sockaddr_in_t *client_addr)
{
    // 检查ID映射表是否已满
    if (idTableCount >= MAX_ID_ENTRIES)
    {
        fprintf(stderr, "ID table full.\n");
        return old_id; // 表满时返回原ID
    }

    // 建立ID映射关系
    idTable[idTableCount].old_id = old_id;            // 保存原始ID
    idTable[idTableCount].client_addr = *client_addr; // 保存客户端地址
    idTable[idTableCount].completed = 0;              // 标记为未完成
    return (uint16_t)idTableCount++;                  // 返回新ID（使用数组索引）
}

// 根据新ID查找原始ID和客户端地址
int find_original_id(uint16_t new_id, uint16_t *old_id, sockaddr_in_t *client_addr)
{
    // 检查ID是否有效且未完成
    if (new_id < MAX_ID_ENTRIES && idTable[new_id].completed == 0)
    {
        *old_id = idTable[new_id].old_id;           // 返回原始ID
        *client_addr = idTable[new_id].client_addr; // 返回客户端地址
        idTable[new_id].completed = 1;              // 标记为已完成
        return DNS_SUCCESS;                         // 成功
    }
    return DNS_ERROR; // 失败
}

// 构建DNS响应数据包
int build_response(char *response, const char *request, int request_len,
                   const char *ip, int *response_len)
{
    // 参数有效性检查
    if (request_len < sizeof(DnsHeader) || !ip)
    {
        return DNS_INVALID_PACKET;
    }

    // 复制请求数据包作为响应基础
    memcpy(response, request, request_len);
    DnsHeader *header = (DnsHeader *)response;

    // 设置响应标志位（标准查询响应）
    header->flags = htons(0x8180);

    // 设置答案数量（如果IP是0.0.0.0表示域名被屏蔽，答案数为0）
    header->answer_count = strcmp(ip, "0.0.0.0") == 0 ? 0 : htons(1);

    // 如果是屏蔽域名，直接返回无答案的响应
    if (strcmp(ip, "0.0.0.0") == 0)
    {
        *response_len = request_len;
        return DNS_SUCCESS;
    }

    // 构建DNS答案记录
    int offset = request_len;

    // 域名指针（指向问题部分的域名）
    uint16_t namePtr = htons(0xc00c);
    memcpy(response + offset, &namePtr, 2);
    offset += 2;

    // 记录类型（A记录）
    uint16_t typeA = htons(1);
    memcpy(response + offset, &typeA, 2);
    offset += 2;

    // 记录类别（IN - Internet）
    uint16_t classIN = htons(1);
    memcpy(response + offset, &classIN, 2);
    offset += 2;

    // TTL（生存时间）
    uint32_t ttl = htonl(120);
    memcpy(response + offset, &ttl, 4);
    offset += 4;

    // 数据长度（IPv4地址长度为4字节）
    uint16_t rdLength = htons(4);
    memcpy(response + offset, &rdLength, 2);
    offset += 2;

    // IP地址数据
    uint32_t ipAddr = inet_addr(ip);
    if (ipAddr == INADDR_NONE)
    {
        return DNS_INVALID_PACKET; // IP地址格式错误
    }
    memcpy(response + offset, &ipAddr, 4);
    offset += 4;

    *response_len = offset;
    return DNS_SUCCESS;
}
