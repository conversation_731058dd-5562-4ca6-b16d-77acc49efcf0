#include "../include/common.h"

// 从配置文件加载本地域名映射表
int load_local_table(const char *filename) {
    FILE *file = fopen(filename, "r");
    if (!file) {
        fprintf(stderr, "Failed to open %s: %s\n", filename, strerror(errno));
        return 0;
    }

    char line[512];
    localTableCount = 1; // 从索引1开始，保留索引0
    
    while (fgets(line, sizeof(line), file) && localTableCount < MAX_LOCAL_ENTRIES) {
        // 移除行尾的换行符
        line[strcspn(line, "\r\n")] = '\0';
        
        // 跳过空行和注释行
        if (line[0] == '\0' || line[0] == '#') {
            continue;
        }
        
        // 解析IP地址和域名（格式：IP 域名）
        char *ip = strtok(line, " \t");
        char *domain = strtok(NULL, " \t\n");
        
        if (ip && domain) {
            // 分配内存并复制字符串
            localTable[localTableCount].ip = platform_strdup(ip);
            localTable[localTableCount].domain = platform_strdup(domain);
            
            if (!localTable[localTableCount].ip || !localTable[localTableCount].domain) {
                fprintf(stderr, "Memory allocation failed for table entry.\n");
                free(localTable[localTableCount].ip);
                free(localTable[localTableCount].domain);
                continue;
            }
            localTableCount++;
        }
    }

    fclose(file);
    printf("Loaded %d entries from %s.\n", localTableCount - 1, filename);
    return localTableCount - 1;
}

// 记录DNS查询日志
void log_query(uint16_t id, const char *domain, const char *ip, DnsSourceType source) {
    platform_time_t time_info;
    platform_get_time(&time_info);
    
    printf("[%02d:%02d:%02d.%03d] ID=%u, Domain=%s, IP=%s, Source=%s\n",
           time_info.hour, time_info.minute, time_info.second, time_info.millisecond,
           id, domain, ip, dns_source_to_string(source));
}

// 清理所有资源
void cleanup_resources(void) {
    // 释放本地表内存
    for (int i = 0; i < localTableCount; i++) {
        free(localTable[i].domain);
        free(localTable[i].ip);
    }
    
    // 释放缓存内存
    for (int i = 0; i < cacheCount; i++) {
        free(cache[i].domain);
        free(cache[i].ip);
    }
    
    // 清理网络资源
    network_cleanup();
}

// DNS来源类型转字符串
const char* dns_source_to_string(DnsSourceType source) {
    switch (source) {
        case DNS_SOURCE_CACHE:    return "Cache";
        case DNS_SOURCE_LOCAL:    return "Local";
        case DNS_SOURCE_BLOCKED:  return "Blocked";
        case DNS_SOURCE_EXTERNAL: return "External";
        default:                  return "Unknown";
    }
}

// 检查是否为屏蔽域名
int is_blocked_domain(const char *ip) {
    return strcmp(ip, "0.0.0.0") == 0;
}
