#ifndef COMMON_H
#define COMMON_H

#include "platform.h"
#include "dns_types.h"
#include "network_wrapper.h"

// 全局变量声明
extern DnsEntry localTable[MAX_LOCAL_ENTRIES];
extern int localTableCount;
extern CacheEntry cache[MAX_CACHE_ENTRIES];
extern int cacheCount;
extern IdMapping idTable[MAX_ID_ENTRIES];
extern int idTableCount;
extern char domainBuffer[MAX_DOMAIN_LENGTH];

// 核心功能函数声明

// 配置文件处理
int load_local_table(const char *filename);

// 域名解析
int extract_domain(const char *packet, int length, char *domain);

// 查找功能
int search_local_table(const char *domain);
int search_cache(const char *domain);

// 缓存管理
void insert_cache(const char *domain, const char *ip, int ttl);
void update_cache_ttl(void);

// ID映射管理
uint16_t generate_new_id(uint16_t old_id, const sockaddr_in_t *client_addr);
int find_original_id(uint16_t new_id, uint16_t *old_id, sockaddr_in_t *client_addr);

// DNS响应构建
int build_response(char *response, const char *request, int request_len, 
                   const char *ip, int *response_len);

// 日志记录
void log_query(uint16_t id, const char *domain, const char *ip, DnsSourceType source);

// 资源清理
void cleanup_resources(void);

// 工具函数
const char* dns_source_to_string(DnsSourceType source);
int is_blocked_domain(const char *ip);

#endif // COMMON_H
