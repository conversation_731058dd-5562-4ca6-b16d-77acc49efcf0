#ifndef PLATFORM_H
#define PLATFORM_H

// 平台检测
#ifdef _WIN32
    #define PLATFORM_WINDOWS
#elif defined(__linux__)
    #define PLATFORM_LINUX
#elif defined(__APPLE__)
    #define PLATFORM_MACOS
#else
    #error "Unsupported platform"
#endif

// Windows平台特定包含
#ifdef PLATFORM_WINDOWS
    #define _CRT_SECURE_NO_WARNINGS
    #define _WINSOCK_DEPRECATED_NO_WARNINGS
    #include <winsock2.h>
    #include <windows.h>
    #include <io.h>
    #pragma comment(lib, "ws2_32.lib")
#endif

// Linux/Unix平台特定包含
#if defined(PLATFORM_LINUX) || defined(PLATFORM_MACOS)
    #include <sys/socket.h>
    #include <sys/time.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
    #include <unistd.h>
    #include <fcntl.h>
    #include <errno.h>
    #include <string.h>
#endif

// 通用包含
#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <time.h>

// 平台相关类型定义
#ifdef PLATFORM_WINDOWS
    typedef SOCKET socket_t;
    typedef SOCKADDR_IN sockaddr_in_t;
    typedef int socklen_t;
    #define INVALID_SOCKET_VALUE INVALID_SOCKET
    #define SOCKET_ERROR_VALUE SOCKET_ERROR
#else
    typedef int socket_t;
    typedef struct sockaddr_in sockaddr_in_t;
    #define INVALID_SOCKET_VALUE -1
    #define SOCKET_ERROR_VALUE -1
    #define closesocket close
#endif

// 错误处理宏
#ifdef PLATFORM_WINDOWS
    #define GET_SOCKET_ERROR() WSAGetLastError()
    #define WOULD_BLOCK WSAEWOULDBLOCK
#else
    #define GET_SOCKET_ERROR() errno
    #define WOULD_BLOCK EAGAIN
#endif

// 时间结构定义
typedef struct {
    int year;
    int month;
    int day;
    int hour;
    int minute;
    int second;
    int millisecond;
} platform_time_t;

// 平台抽象函数声明
int platform_init(void);
void platform_cleanup(void);
void platform_sleep(int milliseconds);
void platform_get_time(platform_time_t *time_info);
char* platform_strdup(const char* str);
int platform_set_nonblocking(socket_t sock);

#endif // PLATFORM_H
