#ifndef NETWORK_WRAPPER_H
#define NETWORK_WRAPPER_H

#include "platform.h"

// 网络配置常量
#define DNS_PORT 53
#define LOCAL_ADDRESS "127.0.0.1"
#define DEFAULT_DNS_SERVER "*******"

// 网络初始化和清理
int network_init(void);
void network_cleanup(void);

// 套接字操作
socket_t network_create_socket(void);
int network_bind_socket(socket_t sock, const char* address, int port);
int network_set_nonblocking(socket_t sock);
void network_close_socket(socket_t sock);

// 数据收发
int network_sendto(socket_t sock, const char* data, int len, 
                   const sockaddr_in_t* addr, socklen_t addr_len);
int network_recvfrom(socket_t sock, char* buffer, int buffer_size,
                     sockaddr_in_t* addr, socklen_t* addr_len);

// 地址操作
void network_setup_address(sockaddr_in_t* addr, const char* ip, int port);
int network_select_timeout(socket_t sock, int timeout_seconds);

// 错误处理
int network_get_last_error(void);
const char* network_error_string(int error_code);
int network_is_would_block_error(int error_code);

#endif // NETWORK_WRAPPER_H
