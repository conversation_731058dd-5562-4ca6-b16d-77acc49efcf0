#define _CRT_SECURE_NO_WARNINGS
#define _WINSOCK_DEPRECATED_NO_WARNINGS

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <winsock2.h>
#include <windows.h>
#include <time.h>
#include <stdint.h>
#pragma comment(lib, "ws2_32.lib")  // ���� Winsock ��

#define BUFFER_SIZE 1024            // ��������С
#define DNS_PORT 53                 // DNSĬ�϶˿ں� 
#define MAX_DOMAIN_LENGTH 255       // �����������
#define MAX_LOCAL_ENTRIES 1500      // ��󱾵�����ӳ�������
#define MAX_ID_ENTRIES 4096         // ���IDӳ�������
#define LOCAL_ADDRESS "127.0.0.1"   // ����DNS��ַ
#define DEFAULT_DNS_SERVER "*******"// ����DNS��ַ
#define NOT_FOUND -1                // ����ʧ��
#define CONFIG_FILE "dnsrelay.txt"  // �����ļ�

// DNS ����ͷ�ṹ��
typedef struct {
    uint16_t id;                    // ���ı�ʶ
    uint16_t flags;                 
    uint16_t question_count;
    uint16_t answer_count;
    uint16_t authority_count;
    uint16_t additional_count;
} DnsHeader;

// ���ر���Ŀ
typedef struct {
    char* domain;                   // ����
    char* ip;                       // IP��ַ
} DnsEntry;

// ID ת������Ŀ
typedef struct {
    uint16_t old_id;                // ԭʼID
    SOCKADDR_IN client_addr;        // �ͻ��˵�ַ
    int completed;                  // ��Ŀ״̬
} IdMapping;

// ������Ŀ
typedef struct {
    char* domain;                   // ����
    char* ip;                       // IP��ַ
    int ttl;                        // ���ʱ��
} CacheEntry;

// ȫ�ֱ���
static DnsEntry localTable[MAX_LOCAL_ENTRIES];  // ����ӳ���
static int localTableCount = 0;                 // ���ر�����
static CacheEntry cache[256];                   // ����
static int cacheCount = 0;                      // ��������
static IdMapping idTable[MAX_ID_ENTRIES];       // IDӳ���
static int idTableCount = 0;                    // ID����
static char domainBuffer[MAX_DOMAIN_LENGTH];    // ��ʱ����

// ��ʼ������
static int initializeNetwork(SOCKET* localSocket, SOCKET* serverSocket, SOCKADDR_IN* localAddr, SOCKADDR_IN* serverAddr) {
    printf("Designed by Haokun Tian.\n");

    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        fprintf(stderr, "WSAStartup failed: %d\n", WSAGetLastError());
        return -1;
    }

    *localSocket = socket(AF_INET, SOCK_DGRAM, 0);
    *serverSocket = socket(AF_INET, SOCK_DGRAM, 0);
    if (*localSocket == INVALID_SOCKET || *serverSocket == INVALID_SOCKET) {
        fprintf(stderr, "Socket creation failed: %d\n", WSAGetLastError());
        WSACleanup();
        return -1;
    }

    unsigned long nonBlocking = 1;
    if (ioctlsocket(*localSocket, FIONBIO, &nonBlocking) || ioctlsocket(*serverSocket, FIONBIO, &nonBlocking)) {
        fprintf(stderr, "Set non-blocking failed: %d\n", WSAGetLastError());
        closesocket(*localSocket);
        closesocket(*serverSocket);
        WSACleanup();
        return -1;
    }

    localAddr->sin_family = AF_INET;
    localAddr->sin_port = htons(DNS_PORT);
    localAddr->sin_addr.s_addr = inet_addr(LOCAL_ADDRESS);
    serverAddr->sin_family = AF_INET;
    serverAddr->sin_port = htons(DNS_PORT);
    serverAddr->sin_addr.s_addr = inet_addr(DEFAULT_DNS_SERVER);

    if (bind(*localSocket, (SOCKADDR*)localAddr, sizeof(*localAddr)) != 0) {
        fprintf(stderr, "Bind failed: %d\n", WSAGetLastError());
        closesocket(*localSocket);
        closesocket(*serverSocket);
        WSACleanup();
        return -1;
    }

    printf("Binding port %d...\n", DNS_PORT);
    Sleep(100);
    printf("Bind successful.\n");
    return 0;
}

// ��ʼ�����ر�
static int loadLocalTable(const char* filename) {
    FILE* file = fopen(filename, "r");
    if (!file) {
        fprintf(stderr, "Failed to open %s: %s\n", filename, strerror(errno));
        return 0;
    }

    char line[512];
    localTableCount = 1;
    while (fgets(line, sizeof(line), file) && localTableCount < MAX_LOCAL_ENTRIES) {
        line[strcspn(line, "\r\n")] = '\0';
        char* ip = strtok(line, " \t");
        char* domain = strtok(NULL, " \t\n");
        if (ip && domain) {
            localTable[localTableCount].ip = _strdup(ip);
            localTable[localTableCount].domain = _strdup(domain);
            if (!localTable[localTableCount].ip || !localTable[localTableCount].domain) {
                fprintf(stderr, "Memory allocation failed for table entry.\n");
                free(localTable[localTableCount].ip);
                free(localTable[localTableCount].domain);
                continue;
            }
            localTableCount++;
        }
    }

    fclose(file);
    printf("Loaded %d entries from %s.\n", localTableCount, filename);
    return localTableCount;
}

// ��ȡ����
static int extractDomain(const char* packet, int length, char* domain) {
    if (length < sizeof(DnsHeader) + 1) {
        return -1;
    }

    int offset = sizeof(DnsHeader);
    int domainPos = 0;
    while (offset < length && packet[offset] != 0) {
        int len = (unsigned char)packet[offset];
        if (len > 63 || offset + len + 1 >= length || domainPos + len + 1 >= MAX_DOMAIN_LENGTH) {
            return -1;
        }
        offset++;
        for (int i = 0; i < len; i++) {
            domain[domainPos++] = packet[offset++];
        }
        if (packet[offset] != 0) {
            domain[domainPos++] = '.';
        }
    }
    domain[domainPos] = '\0';
    return offset + 1;
}

// ���ұ��ر�
static int searchLocalTable(const char* domain) {
    for (int i = 0; i < localTableCount; i++) {
        if (localTable[i].domain && strcmp(localTable[i].domain, domain) == 0) {
            return i;
        }
    }
    return NOT_FOUND;
}

// ���һ���
static int searchCache(const char* domain) {
    for (int i = 0; i < cacheCount; i++) {
        if (cache[i].domain && strcmp(cache[i].domain, domain) == 0 && cache[i].ttl > 0) {
            return i;
        }
    }
    return NOT_FOUND;
}

// ���뻺��
static void insertCache(const char* domain, const char* ip, int ttl) {
    if (cacheCount >= 256) {
        cacheCount = 255; // ���滻���ԣ�������ʵ�� LRU
    }

    cache[cacheCount].domain = _strdup(domain);
    cache[cacheCount].ip = _strdup(ip);
    cache[cacheCount].ttl = ttl;
    if (!cache[cacheCount].domain || !cache[cacheCount].ip) {
        free(cache[cacheCount].domain);
        free(cache[cacheCount].ip);
        return;
    }
    cacheCount++;
}

// ���»��� TTL
static void updateCacheTTL() {
    for (int i = 0; i < cacheCount; i++) {
        if (cache[i].ttl > 0) {
            cache[i].ttl--;
        }
        if (cache[i].ttl <= 0 && cache[i].domain) {
            free(cache[i].domain);
            free(cache[i].ip);
            cache[i].domain = NULL;
            cache[i].ip = NULL;
        }
    }
}

// ������ ID
static uint16_t generateNewId(uint16_t oldId, SOCKADDR_IN clientAddr) {
    if (idTableCount >= MAX_ID_ENTRIES) {
        fprintf(stderr, "ID table full.\n");
        return oldId;
    }

    idTable[idTableCount].old_id = oldId;
    idTable[idTableCount].client_addr = clientAddr;
    idTable[idTableCount].completed = 0;
    return (uint16_t)idTableCount++;
}

// ����ԭʼ ID
static int findOriginalId(uint16_t newId, uint16_t* oldId, SOCKADDR_IN* clientAddr) {
    if (newId < MAX_ID_ENTRIES && idTable[newId].completed == 0) {
        *oldId = idTable[newId].old_id;
        *clientAddr = idTable[newId].client_addr;
        idTable[newId].completed = 1;
        return 0;
    }
    return -1;
}

// ���� DNS ��Ӧ
static int buildResponse(char* response, const char* request, int requestLen, const char* ip, int* responseLen) {
    if (requestLen < sizeof(DnsHeader) || !ip) {
        return -1;
    }

    memcpy(response, request, requestLen);
    DnsHeader* header = (DnsHeader*)response;
    header->flags = htons(0x8180);
    header->answer_count = strcmp(ip, "0.0.0.0") == 0 ? 0 : htons(1);

    if (strcmp(ip, "0.0.0.0") == 0) {
        *responseLen = requestLen;
        return 0;
    }

    int offset = requestLen;
    uint16_t namePtr = htons(0xc00c);
    memcpy(response + offset, &namePtr, 2);
    offset += 2;

    uint16_t typeA = htons(1);
    memcpy(response + offset, &typeA, 2);
    offset += 2;

    uint16_t classIN = htons(1);
    memcpy(response + offset, &classIN, 2);
    offset += 2;

    uint32_t ttl = htonl(120);
    memcpy(response + offset, &ttl, 4);
    offset += 4;

    uint16_t rdLength = htons(4);
    memcpy(response + offset, &rdLength, 2);
    offset += 2;

    uint32_t ipAddr = inet_addr(ip);
    if (ipAddr == INADDR_NONE) {
        return -1;
    }
    memcpy(response + offset, &ipAddr, 4);
    offset += 4;

    *responseLen = offset;
    return 0;
}

// ��־���
static void logQuery(uint16_t id, const char* domain, const char* ip, const char* source) {
    SYSTEMTIME time;
    GetLocalTime(&time);
    printf("[%02d:%02d:%02d.%03d] ID=%u, Domain=%s, IP=%s, Source=%s\n",
        time.wHour, time.wMinute, time.wSecond, time.wMilliseconds, id, domain, ip, source);
}

// ������Դ
static void cleanup(SOCKET localSocket, SOCKET serverSocket) {
    for (int i = 0; i < localTableCount; i++) {
        free(localTable[i].domain);
        free(localTable[i].ip);
    }
    for (int i = 0; i < cacheCount; i++) {
        free(cache[i].domain);
        free(cache[i].ip);
    }
    closesocket(localSocket);
    closesocket(serverSocket);
    WSACleanup();
}

int main() {
    SOCKET localSocket, serverSocket;
    SOCKADDR_IN localAddr, serverAddr;

    // ��ʼ��
    if (initializeNetwork(&localSocket, &serverSocket, &localAddr, &serverAddr) != 0) {
        return 1;
    }

    if (loadLocalTable(CONFIG_FILE) == 0) {
        cleanup(localSocket, serverSocket);
        return 1;
    }

    // ��ѭ��
    char request[BUFFER_SIZE];
    char response[BUFFER_SIZE];
    SOCKADDR_IN clientAddr;
    int clientAddrLen = sizeof(clientAddr);

    while (1) {
        updateCacheTTL();

        int recvLen = recvfrom(localSocket, request, BUFFER_SIZE, 0, (SOCKADDR*)&clientAddr, &clientAddrLen);
        if (recvLen == SOCKET_ERROR) {
            if (WSAGetLastError() != WSAEWOULDBLOCK) {
                fprintf(stderr, "recvfrom failed: %d\n", WSAGetLastError());
            }
            continue;
        }
        if (recvLen < sizeof(DnsHeader)) {
            continue;
        }

        if (extractDomain(request, recvLen, domainBuffer) == -1) {
            continue;
        }

        int cacheIndex = searchCache(domainBuffer);
        int localIndex = searchLocalTable(domainBuffer);
        DnsHeader* header = (DnsHeader*)request;
        uint16_t oldId = ntohs(header->id);
        int responseLen;

        if (cacheIndex != NOT_FOUND) {
            if (buildResponse(response, request, recvLen, cache[cacheIndex].ip, &responseLen) == 0) {
                sendto(localSocket, response, responseLen, 0, (SOCKADDR*)&clientAddr, clientAddrLen);
                logQuery(oldId, domainBuffer, cache[cacheIndex].ip, "Cache");
            }
        }
        else if (localIndex != NOT_FOUND) {
            if (buildResponse(response, request, recvLen, localTable[localIndex].ip, &responseLen) == 0) {
                sendto(localSocket, response, responseLen, 0, (SOCKADDR*)&clientAddr, clientAddrLen);
                logQuery(oldId, domainBuffer, localTable[localIndex].ip, strcmp(localTable[localIndex].ip, "0.0.0.0") == 0 ? "Blocked" : "Local");
            }
        }
        else {
            uint16_t newId = generateNewId(oldId, clientAddr);
            header->id = htons(newId);
            sendto(serverSocket, request, recvLen, 0, (SOCKADDR*)&serverAddr, sizeof(serverAddr));

            fd_set readSet;
            FD_ZERO(&readSet);
            FD_SET(serverSocket, &readSet);
            struct timeval timeout = { 5, 0 };
            if (select(0, &readSet, NULL, NULL, &timeout) <= 0) {
                continue;
            }

            recvLen = recvfrom(serverSocket, response, BUFFER_SIZE, 0, NULL, NULL);
            if (recvLen < sizeof(DnsHeader)) {
                continue;
            }

            header = (DnsHeader*)response;
            uint16_t responseId = ntohs(header->id);
            uint16_t originalId;
            SOCKADDR_IN originalClient;
            if (findOriginalId(responseId, &originalId, &originalClient) != 0) {
                continue;
            }
            header->id = htons(originalId);

            int offset = sizeof(DnsHeader);
            while (offset < recvLen && response[offset] != 0) {
                offset += (unsigned char)response[offset] + 1;
            }
            offset += 5;
            int answerCount = ntohs(header->answer_count);
            char ip[16];
            for (int i = 0; i < answerCount && offset + 12 <= recvLen; i++) {
                if ((unsigned char)response[offset] == 0xc0) {
                    offset += 2;
                }
                uint16_t type = ntohs(*(uint16_t*)(response + offset));
                offset += 4;
                uint32_t ttl = ntohl(*(uint32_t*)(response + offset));
                offset += 4;
                uint16_t dataLen = ntohs(*(uint16_t*)(response + offset));
                offset += 2;
                if (type == 1 && dataLen == 4) {
                    sprintf(ip, "%d.%d.%d.%d", (unsigned char)response[offset],
                        (unsigned char)response[offset + 1],
                        (unsigned char)response[offset + 2],
                        (unsigned char)response[offset + 3]);
                    insertCache(domainBuffer, ip, ttl);
                    logQuery(originalId, domainBuffer, ip, "External");
                }
                offset += dataLen;
            }

            sendto(localSocket, response, recvLen, 0, (SOCKADDR*)&originalClient, sizeof(originalClient));
        }
    }

    cleanup(localSocket, serverSocket);
    return 0;
}