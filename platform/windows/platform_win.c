#include "../../include/platform.h"

// Windows平台初始化
int platform_init(void) {
    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        fprintf(stderr, "WSAStartup failed: %d\n", WSAGetLastError());
        return -1;
    }
    return 0;
}

// Windows平台清理
void platform_cleanup(void) {
    WSACleanup();
}

// Windows睡眠函数
void platform_sleep(int milliseconds) {
    Sleep(milliseconds);
}

// Windows获取当前时间
void platform_get_time(platform_time_t *time_info) {
    SYSTEMTIME st;
    GetLocalTime(&st);
    
    time_info->year = st.wYear;
    time_info->month = st.wMonth;
    time_info->day = st.wDay;
    time_info->hour = st.wHour;
    time_info->minute = st.wMinute;
    time_info->second = st.wSecond;
    time_info->millisecond = st.wMilliseconds;
}

// Windows字符串复制
char* platform_strdup(const char* str) {
    return _strdup(str);
}

// Windows设置非阻塞模式
int platform_set_nonblocking(socket_t sock) {
    unsigned long mode = 1;
    return ioctlsocket(sock, FIONBIO, &mode);
}
