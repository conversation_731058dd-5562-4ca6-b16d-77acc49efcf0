#include "../../include/network_wrapper.h"

// Windows网络初始化
int network_init(void) {
    return platform_init();
}

// Windows网络清理
void network_cleanup(void) {
    platform_cleanup();
}

// 创建套接字
socket_t network_create_socket(void) {
    return socket(AF_INET, SOCK_DGRAM, 0);
}

// 绑定套接字
int network_bind_socket(socket_t sock, const char* address, int port) {
    sockaddr_in_t addr;
    network_setup_address(&addr, address, port);
    return bind(sock, (struct sockaddr*)&addr, sizeof(addr));
}

// 设置非阻塞模式
int network_set_nonblocking(socket_t sock) {
    return platform_set_nonblocking(sock);
}

// 关闭套接字
void network_close_socket(socket_t sock) {
    closesocket(sock);
}

// 发送数据
int network_sendto(socket_t sock, const char* data, int len, 
                   const sockaddr_in_t* addr, socklen_t addr_len) {
    return sendto(sock, data, len, 0, (const struct sockaddr*)addr, addr_len);
}

// 接收数据
int network_recvfrom(socket_t sock, char* buffer, int buffer_size,
                     sockaddr_in_t* addr, socklen_t* addr_len) {
    return recvfrom(sock, buffer, buffer_size, 0, (struct sockaddr*)addr, addr_len);
}

// 设置地址结构
void network_setup_address(sockaddr_in_t* addr, const char* ip, int port) {
    memset(addr, 0, sizeof(*addr));
    addr->sin_family = AF_INET;
    addr->sin_port = htons(port);
    addr->sin_addr.s_addr = inet_addr(ip);
}

// 带超时的select
int network_select_timeout(socket_t sock, int timeout_seconds) {
    fd_set readSet;
    struct timeval timeout;
    
    FD_ZERO(&readSet);
    FD_SET(sock, &readSet);
    
    timeout.tv_sec = timeout_seconds;
    timeout.tv_usec = 0;
    
    return select(0, &readSet, NULL, NULL, &timeout);
}

// 获取最后的错误码
int network_get_last_error(void) {
    return WSAGetLastError();
}

// 错误码转字符串
const char* network_error_string(int error_code) {
    static char buffer[256];
    FormatMessageA(FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
                   NULL, error_code, 0, buffer, sizeof(buffer), NULL);
    return buffer;
}

// 检查是否为WOULD_BLOCK错误
int network_is_would_block_error(int error_code) {
    return error_code == WSAEWOULDBLOCK;
}
