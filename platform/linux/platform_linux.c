#include "../../include/platform.h"

// Linux平台初始化
int platform_init(void) {
    // Linux不需要特殊的网络库初始化
    return 0;
}

// Linux平台清理
void platform_cleanup(void) {
    // Linux不需要特殊的网络库清理
}

// Linux睡眠函数
void platform_sleep(int milliseconds) {
    usleep(milliseconds * 1000);  // usleep使用微秒
}

// Linux获取当前时间
void platform_get_time(platform_time_t *time_info) {
    struct timeval tv;
    struct tm *tm_info;
    
    gettimeofday(&tv, NULL);
    tm_info = localtime(&tv.tv_sec);
    
    time_info->year = tm_info->tm_year + 1900;
    time_info->month = tm_info->tm_mon + 1;
    time_info->day = tm_info->tm_mday;
    time_info->hour = tm_info->tm_hour;
    time_info->minute = tm_info->tm_min;
    time_info->second = tm_info->tm_sec;
    time_info->millisecond = tv.tv_usec / 1000;
}

// Linux字符串复制
char* platform_strdup(const char* str) {
    return strdup(str);
}

// Linux设置非阻塞模式
int platform_set_nonblocking(socket_t sock) {
    int flags = fcntl(sock, F_GETFL, 0);
    if (flags == -1) {
        return -1;
    }
    return fcntl(sock, F_SETFL, flags | O_NONBLOCK);
}
