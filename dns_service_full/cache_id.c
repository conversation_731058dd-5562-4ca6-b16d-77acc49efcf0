#include "cache_id.h"
#include <string.h>
#include <stdlib.h>

CacheEntry cacheTable[CACHE_SIZE];
int        cacheCount = 0;
IdMapping  idTable[MAX_ID_ENTRIES];
int        idCount    = 0;

int searchCache(const char *domain)
{
    for (int i = 0; i < cacheCount; i++) {
        if (cacheTable[i].domain &&
            strcmp(cacheTable[i].domain, domain) == 0 &&
            cacheTable[i].ttl > 0)
            return i;
    }
    return NOT_FOUND;
}

void insertCache(const char *domain, const char *ip, int ttl)
{
    if (cacheCount >= CACHE_SIZE) cacheCount = CACHE_SIZE - 1;
    cacheTable[cacheCount].domain = _strdup(domain);
    cacheTable[cacheCount].ip     = _strdup(ip);
    cacheTable[cacheCount].ttl    = ttl;
    cacheCount++;
}

void updateCacheTTL(void)
{
    for (int i = 0; i < cacheCount; i++) {
        if (--cacheTable[i].ttl <= 0) {
            free(cacheTable[i].domain);
            free(cacheTable[i].ip);
            cacheTable[i].domain = cacheTable[i].ip = NULL;
        }
    }
}

uint16_t generateNewId(uint16_t oldId, SOCKADDR_IN clientAddr)
{
    if (idCount >= MAX_ID_ENTRIES) return oldId;
    idTable[idCount] = (IdMapping){ oldId, clientAddr, 0 };
    return (uint16_t)(idCount++);
}

int findOriginalId(uint16_t newId, uint16_t *oldId, SOCKADDR_IN *clientAddr)
{
    if (newId < idCount && idTable[newId].completed == 0) {
        *oldId      = idTable[newId].oldId;
        *clientAddr = idTable[newId].clientAddr;
        idTable[newId].completed = 1;
        return 0;
    }
    return -1;
}
