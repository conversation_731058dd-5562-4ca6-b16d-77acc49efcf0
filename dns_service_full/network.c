#define _WINSOCK_DEPRECATED_NO_WARNINGS
#include "network.h"
#include <stdio.h>

#pragma comment(lib, "ws2_32.lib")

int initializeNetwork(SOCKET *localSock, SOCKET *serverSock, 
                      struct sockaddr_in *localAddr, struct sockaddr_in *srvAddr)
{
    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2,2), &wsaData) != 0) {
        fprintf(stderr, "WSAStartup failed: %d\n", WSAGetLastError());
        return -1;
    }

    *localSock = socket(AF_INET, SOCK_DGRAM, 0);
    *serverSock = socket(AF_INET, SOCK_DGRAM, 0);
    if (*localSock == INVALID_SOCKET || *serverSock == INVALID_SOCKET) {
        fprintf(stderr, "Socket creation failed: %d\n", WSAGetLastError());
        WSACleanup();
        return -1;
    }

    unsigned long mode = 1;
    ioctlsocket(*localSock, FIONBIO, &mode);
    ioctlsocket(*serverSock, FIONBIO, &mode);

    localAddr->sin_family = AF_INET;
    localAddr->sin_port   = htons(DNS_PORT);
    localAddr->sin_addr.s_addr = inet_addr(LOCAL_ADDRESS);

    srvAddr->sin_family = AF_INET;
    srvAddr->sin_port   = htons(DNS_PORT);
    srvAddr->sin_addr.s_addr = inet_addr(DEFAULT_DNS_SERVER);

    if (bind(*localSock, (struct sockaddr*)localAddr, sizeof(*localAddr)) != 0) {
        fprintf(stderr, "Bind failed: %d\n", WSAGetLastError());
        cleanupNetwork(*localSock, *serverSock);
        return -1;
    }

    printf("Network initialized, listening on %s:%d\n", LOCAL_ADDRESS, DNS_PORT);
    return 0;
}

void cleanupNetwork(SOCKET localSock, SOCKET serverSock)
{
    closesocket(localSock);
    closesocket(serverSock);
    WSACleanup();
}
