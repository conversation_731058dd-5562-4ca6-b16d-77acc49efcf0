#include "dnsproc.h"
#include <stdio.h>
#include <string.h>
#include <windows.h>

int extractDomain(const char *packet, int len, char *outDomain)
{
    if (len < sizeof(DnsHeader)+1) return -1;
    int offset = sizeof(DnsHeader), pos = 0;
    while (offset < len && packet[offset] != 0) {
        int l = (unsigned char)packet[offset++];
        if (l <= 0 || l > 63 || pos + l + 1 >= MAX_DOMAIN_LEN)
            return -1;
        memcpy(outDomain+pos, packet+offset, l);
        pos += l; offset += l;
        if (packet[offset] != 0)
            outDomain[pos++] = '.';
    }
    outDomain[pos] = '\0';
    return offset + 1;
}

int buildResponse(char *resp, const char *req, int reqLen,
                  const char *ip, int *respLen)
{
    if (reqLen < sizeof(DnsHeader) || !ip) return -1;
    memcpy(resp, req, reqLen);
    DnsHeader *h = (DnsHeader*)resp;
    h->flags      = htons(0x8180);
    h->ancount    = strcmp(ip,"0.0.0.0")==0 ? 0 : htons(1);
    if (strcmp(ip,"0.0.0.0")==0) {
        *respLen = reqLen; return 0;
    }
    int off = reqLen;
    uint16_t ptr = htons(0xc00c);
    memcpy(resp+off, &ptr, 2); off += 2;
    uint16_t typeA = htons(1), classIN = htons(1), rdlen = htons(4);
    uint32_t ttl   = htonl(120);
    memcpy(resp+off, &typeA, 2); off+=2;
    memcpy(resp+off, &classIN,2); off+=2;
    memcpy(resp+off, &ttl,4);    off+=4;
    memcpy(resp+off, &rdlen,2);  off+=2;
    uint32_t a = inet_addr(ip);
    memcpy(resp+off, &a,4);      off+=4;
    *respLen = off;
    return 0;
}

void logQuery(uint16_t id, const char *domain, 
              const char *ip, const char *src)
{
    SYSTEMTIME t; GetLocalTime(&t);
    printf("[%02d:%02d:%02d.%03d] ID=%u, Domain=%s, IP=%s, Source=%s\n",
           t.wHour,t.wMinute,t.wSecond,t.wMilliseconds,
           id, domain, ip, src);
}
