#ifndef CACHE_ID_H
#define CACHE_ID_H

#include <winsock2.h>
#include <stdint.h>

#define CACHE_SIZE    256
#define MAX_ID_ENTRIES 4096
#define NOT_FOUND      -1

typedef struct {
    char    *domain;
    char    *ip;
    int      ttl;
} CacheEntry;

typedef struct {
    uint16_t    oldId;
    SOCKADDR_IN clientAddr;
    int         completed;
} IdMapping;

extern CacheEntry cacheTable[CACHE_SIZE];
extern int        cacheCount;
extern IdMapping  idTable[MAX_ID_ENTRIES];
extern int        idCount;

int   searchCache(const char *domain);
void  insertCache(const char *domain, const char *ip, int ttl);
void  updateCacheTTL(void);
uint16_t generateNewId(uint16_t oldId, SOCKADDR_IN clientAddr);
int       findOriginalId(uint16_t newId, uint16_t *oldId, SOCKADDR_IN *clientAddr);

#endif // CACHE_ID_H
