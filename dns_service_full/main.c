#include "network.h"
#include "config.h"
#include "cache_id.h"
#include "dnsproc.h"

#include <stdio.h>
#include <winsock2.h>

#define BUFFER_SIZE 1024

int main(void)
{
    SOCKET localSock, serverSock;
    struct sockaddr_in localAddr, serverAddr;

    if (initializeNetwork(&localSock, &serverSock, &localAddr, &serverAddr) != 0)
        return 1;
    if (loadLocalTable("dnsrelay.txt") == 0) {
        cleanupNetwork(localSock, serverSock);
        return 1;
    }

    char  reqBuf[BUFFER_SIZE], respBuf[BUFFER_SIZE], domain[MAX_DOMAIN_LEN];
    int   reqLen, respLen;
    SOCKADDR_IN clientAddr;
    int clientLen = sizeof(clientAddr);

    while (1) {
        updateCacheTTL();

        reqLen = recvfrom(localSock, reqBuf, BUFFER_SIZE, 0,
                          (struct sockaddr*)&clientAddr, &clientLen);
        if (reqLen == SOCKET_ERROR) continue;
        if (extractDomain(reqBuf, reqLen, domain) < 0) continue;

        int locIdx = -1;
        for (int i = 0; i < localTableCount; i++)
            if (strcmp(localTable[i].domain, domain)==0)
                { locIdx = i; break; }

        int cIdx = searchCache(domain);
        if (cIdx != NOT_FOUND) {
            buildResponse(respBuf, reqBuf, reqLen,
                          cacheTable[cIdx].ip, &respLen);
            sendto(localSock, respBuf, respLen, 0,
                   (struct sockaddr*)&clientAddr, clientLen);
            logQuery(ntohs(((DnsHeader*)reqBuf)->id), domain,
                     cacheTable[cIdx].ip, "Cache");
        }
        else if (locIdx != -1) {
            buildResponse(respBuf, reqBuf, reqLen,
                          localTable[locIdx].ip, &respLen);
            sendto(localSock, respBuf, respLen, 0,
                   (struct sockaddr*)&clientAddr, clientLen);
            logQuery(ntohs(((DnsHeader*)reqBuf)->id), domain,
                     localTable[locIdx].ip, "Local");
        }
        else {
            uint16_t oldId = ntohs(((DnsHeader*)reqBuf)->id);
            uint16_t newId = generateNewId(oldId, clientAddr);
            ((DnsHeader*)reqBuf)->id = htons(newId);
            sendto(serverSock, reqBuf, reqLen, 0,
                   (struct sockaddr*)&serverAddr, sizeof(serverAddr));

            fd_set rs; FD_ZERO(&rs); FD_SET(serverSock, &rs);
            struct timeval tv = {5,0};
            if (select(0, &rs, NULL, NULL, &tv) <= 0) continue;

            int rlen = recvfrom(serverSock, respBuf, BUFFER_SIZE, 0, NULL, NULL);
            if (rlen < sizeof(DnsHeader)) continue;

            uint16_t recId = ntohs(((DnsHeader*)respBuf)->id);
            uint16_t origId;
            SOCKADDR_IN origAddr;
            if (findOriginalId(recId, &origId, &origAddr) != 0) continue;
            ((DnsHeader*)respBuf)->id = htons(origId);

            int off = sizeof(DnsHeader);
            while (off < rlen && respBuf[off]!=0) off += (unsigned char)respBuf[off]+1;
            off += 5;
            int ans = ntohs(((DnsHeader*)respBuf)->ancount);
            for (int i = 0; i < ans && off+12 <= rlen; i++) {
                if ((unsigned char)respBuf[off]==0xc0) off+=2;
                uint16_t type = ntohs(*(uint16_t*)(respBuf+off)); off+=4;
                uint32_t ttl  = ntohl(*(uint32_t*)(respBuf+off)); off+=4;
                uint16_t dlen = ntohs(*(uint16_t*)(respBuf+off)); off+=2;
                if (type==1 && dlen==4) {
                    char ipstr[16];
                    sprintf(ipstr, "%u.%u.%u.%u",
                            (unsigned char)respBuf[off],
                            (unsigned char)respBuf[off+1],
                            (unsigned char)respBuf[off+2],
                            (unsigned char)respBuf[off+3]);
                    insertCache(domain, ipstr, (int)ttl);
                    logQuery(origId, domain, ipstr, "External");
                }
                off += dlen;
            }

            sendto(localSock, respBuf, rlen, 0,
                   (struct sockaddr*)&origAddr, sizeof(origAddr));
        }
    }

    cleanupNetwork(localSock, serverSock);
    return 0;
}
