#ifndef DNSPROC_H
#define DNSPROC_H

#include <stdint.h>
#include <winsock2.h>

#define MAX_DOMAIN_LEN 255

typedef struct {
    uint16_t id, flags;
    uint16_t qdcount, ancount, nscount, arcount;
} DnsHeader;

int extractDomain(const char *packet, int len, char *outDomain);
int buildResponse(char *resp, const char *req, int reqLen,
                  const char *ip, int *respLen);
void logQuery(uint16_t id, const char *domain, 
              const char *ip, const char *source);

#endif // DNSPROC_H
