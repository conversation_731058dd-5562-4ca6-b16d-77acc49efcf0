#include "config.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <errno.h>

DnsEntry localTable[MAX_LOCAL_ENTRIES];
int     localTableCount = 0;

int loadLocalTable(const char *filename)
{
    FILE *fp = fopen(filename, "r");
    if (!fp) {
        fprintf(stderr, "Failed to open %s: %s\n", filename, strerror(errno));
        return 0;
    }

    char line[512];
    localTableCount = 0;
    while (fgets(line, sizeof(line), fp) && localTableCount < MAX_LOCAL_ENTRIES) {
        line[strcspn(line, "\r\n")] = '\0';
        char *ip     = strtok(line, " \t");
        char *domain = strtok(NULL, " \t");
        if (ip && domain) {
            localTable[localTableCount].ip     = _strdup(ip);
            localTable[localTableCount].domain = _strdup(domain);
            if (localTable[localTableCount].ip && localTable[localTableCount].domain) {
                localTableCount++;
            }
        }
    }
    fclose(fp);
    printf("Loaded %d local entries from %s\n", localTableCount, filename);
    return localTableCount;
}
