#define _CRT_SECURE_NO_WARNINGS
#define _WINSOCK_DEPRECATED_NO_WARNINGS

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <winsock2.h>
#include <windows.h>
#include <time.h>
#include <stdint.h>
#pragma comment(lib, "ws2_32.lib") // 链接 Winsock 库

#define BUFFER_SIZE 1024             // 缓冲区大小
#define DNS_PORT 53                  // DNS默认端口号
#define MAX_DOMAIN_LENGTH 255        // 域名最大长度
#define MAX_LOCAL_ENTRIES 1500       // 最大本地域名映射条目数
#define MAX_ID_ENTRIES 4096          // 最大ID映射条目数
#define LOCAL_ADDRESS "127.0.0.1"    // 本地DNS地址
#define DEFAULT_DNS_SERVER "*******" // 外部DNS地址
#define NOT_FOUND -1                 // 查找失败
#define CONFIG_FILE "dnsrelay.txt"   // 配置文件

// DNS 数据包头结构体
typedef struct
{
    uint16_t id;               // 查询标识符
    uint16_t flags;            // 标志位
    uint16_t question_count;   // 问题数量
    uint16_t answer_count;     // 答案数量
    uint16_t authority_count;  // 权威记录数量
    uint16_t additional_count; // 附加记录数量
} DnsHeader;

// 本地表条目结构
typedef struct
{
    char *domain; // 域名
    char *ip;     // IP地址
} DnsEntry;

// ID 转换映射条目结构
typedef struct
{
    uint16_t old_id;         // 原始查询ID
    SOCKADDR_IN client_addr; // 客户端地址
    int completed;           // 条目状态（是否已完成）
} IdMapping;

// 缓存条目结构
typedef struct
{
    char *domain; // 域名
    char *ip;     // IP地址
    int ttl;      // 生存时间
} CacheEntry;

// 全局变量定义
static DnsEntry localTable[MAX_LOCAL_ENTRIES]; // 本地域名映射表
static int localTableCount = 0;                // 本地表条目数
static CacheEntry cache[256];                  // DNS缓存
static int cacheCount = 0;                     // 缓存条目数
static IdMapping idTable[MAX_ID_ENTRIES];      // ID映射表
static int idTableCount = 0;                   // ID映射条目数
static char domainBuffer[MAX_DOMAIN_LENGTH];   // 临时域名缓冲区

// 初始化网络环境和套接字
static int initializeNetwork(SOCKET *localSocket, SOCKET *serverSocket, SOCKADDR_IN *localAddr, SOCKADDR_IN *serverAddr)
{
    printf("Designed by Haokun Tian.\n");

    // 初始化 Winsock
    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0)
    {
        fprintf(stderr, "WSAStartup failed: %d\n", WSAGetLastError());
        return -1;
    }

    // 创建本地和服务器套接字
    *localSocket = socket(AF_INET, SOCK_DGRAM, 0);
    *serverSocket = socket(AF_INET, SOCK_DGRAM, 0);
    if (*localSocket == INVALID_SOCKET || *serverSocket == INVALID_SOCKET)
    {
        fprintf(stderr, "Socket creation failed: %d\n", WSAGetLastError());
        WSACleanup();
        return -1;
    }

    // 设置套接字为非阻塞模式
    unsigned long nonBlocking = 1;
    if (ioctlsocket(*localSocket, FIONBIO, &nonBlocking) || ioctlsocket(*serverSocket, FIONBIO, &nonBlocking))
    {
        fprintf(stderr, "Set non-blocking failed: %d\n", WSAGetLastError());
        closesocket(*localSocket);
        closesocket(*serverSocket);
        WSACleanup();
        return -1;
    }

    // 配置本地地址结构
    localAddr->sin_family = AF_INET;
    localAddr->sin_port = htons(DNS_PORT);
    localAddr->sin_addr.s_addr = inet_addr(LOCAL_ADDRESS);

    // 配置外部DNS服务器地址结构
    serverAddr->sin_family = AF_INET;
    serverAddr->sin_port = htons(DNS_PORT);
    serverAddr->sin_addr.s_addr = inet_addr(DEFAULT_DNS_SERVER);

    // 绑定本地套接字到指定端口
    if (bind(*localSocket, (SOCKADDR *)localAddr, sizeof(*localAddr)) != 0)
    {
        fprintf(stderr, "Bind failed: %d\n", WSAGetLastError());
        closesocket(*localSocket);
        closesocket(*serverSocket);
        WSACleanup();
        return -1;
    }

    printf("Binding port %d...\n", DNS_PORT);
    Sleep(100);
    printf("Bind successful.\n");
    return 0;
}

// 从配置文件加载本地域名映射表
static int loadLocalTable(const char *filename)
{
    FILE *file = fopen(filename, "r");
    if (!file)
    {
        fprintf(stderr, "Failed to open %s: %s\n", filename, strerror(errno));
        return 0;
    }

    char line[512];
    localTableCount = 1; // 从索引1开始，保留索引0
    while (fgets(line, sizeof(line), file) && localTableCount < MAX_LOCAL_ENTRIES)
    {
        // 移除行尾的换行符
        line[strcspn(line, "\r\n")] = '\0';

        // 解析IP地址和域名（格式：IP 域名）
        char *ip = strtok(line, " \t");
        char *domain = strtok(NULL, " \t\n");
        if (ip && domain)
        {
            // 分配内存并复制字符串
            localTable[localTableCount].ip = _strdup(ip);
            localTable[localTableCount].domain = _strdup(domain);
            if (!localTable[localTableCount].ip || !localTable[localTableCount].domain)
            {
                fprintf(stderr, "Memory allocation failed for table entry.\n");
                free(localTable[localTableCount].ip);
                free(localTable[localTableCount].domain);
                continue;
            }
            localTableCount++;
        }
    }

    fclose(file);
    printf("Loaded %d entries from %s.\n", localTableCount, filename);
    return localTableCount;
}

// 从DNS数据包中提取域名
static int extractDomain(const char *packet, int length, char *domain)
{
    // 检查数据包长度是否足够
    if (length < sizeof(DnsHeader) + 1)
    {
        return -1;
    }

    int offset = sizeof(DnsHeader); // 跳过DNS头部
    int domainPos = 0;

    // 解析域名（DNS标准格式：长度+字符串）
    while (offset < length && packet[offset] != 0)
    {
        int len = (unsigned char)packet[offset];
        // 检查长度是否合法
        if (len > 63 || offset + len + 1 >= length || domainPos + len + 1 >= MAX_DOMAIN_LENGTH)
        {
            return -1;
        }
        offset++;

        // 复制域名段
        for (int i = 0; i < len; i++)
        {
            domain[domainPos++] = packet[offset++];
        }

        // 如果不是最后一段，添加点号分隔符
        if (packet[offset] != 0)
        {
            domain[domainPos++] = '.';
        }
    }
    domain[domainPos] = '\0';
    return offset + 1; // 返回域名结束后的偏移量
}

// 在本地域名映射表中查找域名
static int searchLocalTable(const char *domain)
{
    for (int i = 0; i < localTableCount; i++)
    {
        if (localTable[i].domain && strcmp(localTable[i].domain, domain) == 0)
        {
            return i; // 返回找到的索引
        }
    }
    return NOT_FOUND; // 未找到
}

// 在DNS缓存中查找域名
static int searchCache(const char *domain)
{
    for (int i = 0; i < cacheCount; i++)
    {
        // 检查域名匹配且TTL有效
        if (cache[i].domain && strcmp(cache[i].domain, domain) == 0 && cache[i].ttl > 0)
        {
            return i; // 返回找到的索引
        }
    }
    return NOT_FOUND; // 未找到或已过期
}

// 向DNS缓存中插入新条目
static void insertCache(const char *domain, const char *ip, int ttl)
{
    // 简单的缓存替换策略：如果满了就覆盖最后一个
    if (cacheCount >= 256)
    {
        cacheCount = 255; // 简单替换策略，实际应实现 LRU
    }

    // 分配内存并复制数据
    cache[cacheCount].domain = _strdup(domain);
    cache[cacheCount].ip = _strdup(ip);
    cache[cacheCount].ttl = ttl;

    // 检查内存分配是否成功
    if (!cache[cacheCount].domain || !cache[cacheCount].ip)
    {
        free(cache[cacheCount].domain);
        free(cache[cacheCount].ip);
        return;
    }
    cacheCount++;
}

// 更新缓存中所有条目的TTL值
static void updateCacheTTL()
{
    for (int i = 0; i < cacheCount; i++)
    {
        // 递减TTL
        if (cache[i].ttl > 0)
        {
            cache[i].ttl--;
        }

        // 清理过期条目
        if (cache[i].ttl <= 0 && cache[i].domain)
        {
            free(cache[i].domain);
            free(cache[i].ip);
            cache[i].domain = NULL;
            cache[i].ip = NULL;
        }
    }
}

// 生成新的查询ID并建立映射关系
static uint16_t generateNewId(uint16_t oldId, SOCKADDR_IN clientAddr)
{
    // 检查ID映射表是否已满
    if (idTableCount >= MAX_ID_ENTRIES)
    {
        fprintf(stderr, "ID table full.\n");
        return oldId; // 表满时返回原ID
    }

    // 建立ID映射关系
    idTable[idTableCount].old_id = oldId;           // 保存原始ID
    idTable[idTableCount].client_addr = clientAddr; // 保存客户端地址
    idTable[idTableCount].completed = 0;            // 标记为未完成
    return (uint16_t)idTableCount++;                // 返回新ID（使用数组索引）
}

// 根据新ID查找原始ID和客户端地址
static int findOriginalId(uint16_t newId, uint16_t *oldId, SOCKADDR_IN *clientAddr)
{
    // 检查ID是否有效且未完成
    if (newId < MAX_ID_ENTRIES && idTable[newId].completed == 0)
    {
        *oldId = idTable[newId].old_id;           // 返回原始ID
        *clientAddr = idTable[newId].client_addr; // 返回客户端地址
        idTable[newId].completed = 1;             // 标记为已完成
        return 0;                                 // 成功
    }
    return -1; // 失败
}

// 构建DNS响应数据包
static int buildResponse(char *response, const char *request, int requestLen, const char *ip, int *responseLen)
{
    // 参数有效性检查
    if (requestLen < sizeof(DnsHeader) || !ip)
    {
        return -1;
    }

    // 复制请求数据包作为响应基础
    memcpy(response, request, requestLen);
    DnsHeader *header = (DnsHeader *)response;

    // 设置响应标志位（标准查询响应）
    header->flags = htons(0x8180);

    // 设置答案数量（如果IP是0.0.0.0表示域名被屏蔽，答案数为0）
    header->answer_count = strcmp(ip, "0.0.0.0") == 0 ? 0 : htons(1);

    // 如果是屏蔽域名，直接返回无答案的响应
    if (strcmp(ip, "0.0.0.0") == 0)
    {
        *responseLen = requestLen;
        return 0;
    }

    // 构建DNS答案记录
    int offset = requestLen;

    // 域名指针（指向问题部分的域名）
    uint16_t namePtr = htons(0xc00c);
    memcpy(response + offset, &namePtr, 2);
    offset += 2;

    // 记录类型（A记录）
    uint16_t typeA = htons(1);
    memcpy(response + offset, &typeA, 2);
    offset += 2;

    // 记录类别（IN - Internet）
    uint16_t classIN = htons(1);
    memcpy(response + offset, &classIN, 2);
    offset += 2;

    // TTL（生存时间）
    uint32_t ttl = htonl(120);
    memcpy(response + offset, &ttl, 4);
    offset += 4;

    // 数据长度（IPv4地址长度为4字节）
    uint16_t rdLength = htons(4);
    memcpy(response + offset, &rdLength, 2);
    offset += 2;

    // IP地址数据
    uint32_t ipAddr = inet_addr(ip);
    if (ipAddr == INADDR_NONE)
    {
        return -1; // IP地址格式错误
    }
    memcpy(response + offset, &ipAddr, 4);
    offset += 4;

    *responseLen = offset;
    return 0;
}

// 记录DNS查询日志
static void logQuery(uint16_t id, const char *domain, const char *ip, const char *source)
{
    SYSTEMTIME time;
    GetLocalTime(&time);
    printf("[%02d:%02d:%02d.%03d] ID=%u, Domain=%s, IP=%s, Source=%s\n",
           time.wHour, time.wMinute, time.wSecond, time.wMilliseconds, id, domain, ip, source);
}

// 清理资源并关闭程序
static void cleanup(SOCKET localSocket, SOCKET serverSocket)
{
    // 释放本地表内存
    for (int i = 0; i < localTableCount; i++)
    {
        free(localTable[i].domain);
        free(localTable[i].ip);
    }

    // 释放缓存内存
    for (int i = 0; i < cacheCount; i++)
    {
        free(cache[i].domain);
        free(cache[i].ip);
    }

    // 关闭套接字
    closesocket(localSocket);
    closesocket(serverSocket);

    // 清理Winsock
    WSACleanup();
}

int main()
{
    SOCKET localSocket, serverSocket;
    SOCKADDR_IN localAddr, serverAddr;

    // 初始化网络环境
    if (initializeNetwork(&localSocket, &serverSocket, &localAddr, &serverAddr) != 0)
    {
        return 1;
    }

    // 加载本地域名映射表
    if (loadLocalTable(CONFIG_FILE) == 0)
    {
        cleanup(localSocket, serverSocket);
        return 1;
    }

    // 主服务循环
    char request[BUFFER_SIZE];
    char response[BUFFER_SIZE];
    SOCKADDR_IN clientAddr;
    int clientAddrLen = sizeof(clientAddr);

    while (1)
    {
        // 更新缓存TTL
        updateCacheTTL();

        // 接收DNS查询请求
        int recvLen = recvfrom(localSocket, request, BUFFER_SIZE, 0, (SOCKADDR *)&clientAddr, &clientAddrLen);
        if (recvLen == SOCKET_ERROR)
        {
            // 非阻塞模式下的正常情况，继续循环
            if (WSAGetLastError() != WSAEWOULDBLOCK)
            {
                fprintf(stderr, "recvfrom failed: %d\n", WSAGetLastError());
            }
            continue;
        }

        // 检查数据包长度是否足够
        if (recvLen < sizeof(DnsHeader))
        {
            continue;
        }

        // 从请求中提取域名
        if (extractDomain(request, recvLen, domainBuffer) == -1)
        {
            continue; // 域名提取失败，跳过此请求
        }

        // 查找缓存和本地表
        int cacheIndex = searchCache(domainBuffer);
        int localIndex = searchLocalTable(domainBuffer);
        DnsHeader *header = (DnsHeader *)request;
        uint16_t oldId = ntohs(header->id);
        int responseLen;

        // 优先级1：检查缓存
        if (cacheIndex != NOT_FOUND)
        {
            // 从缓存构建响应
            if (buildResponse(response, request, recvLen, cache[cacheIndex].ip, &responseLen) == 0)
            {
                sendto(localSocket, response, responseLen, 0, (SOCKADDR *)&clientAddr, clientAddrLen);
                logQuery(oldId, domainBuffer, cache[cacheIndex].ip, "Cache");
            }
        }
        // 优先级2：检查本地表
        else if (localIndex != NOT_FOUND)
        {
            // 从本地表构建响应
            if (buildResponse(response, request, recvLen, localTable[localIndex].ip, &responseLen) == 0)
            {
                sendto(localSocket, response, responseLen, 0, (SOCKADDR *)&clientAddr, clientAddrLen);
                // 根据IP判断是屏蔽还是本地解析
                logQuery(oldId, domainBuffer, localTable[localIndex].ip,
                         strcmp(localTable[localIndex].ip, "0.0.0.0") == 0 ? "Blocked" : "Local");
            }
        }
        // 优先级3：转发到外部DNS服务器
        else
        {
            // 生成新ID并建立映射
            uint16_t newId = generateNewId(oldId, clientAddr);
            header->id = htons(newId);

            // 转发请求到外部DNS服务器
            sendto(serverSocket, request, recvLen, 0, (SOCKADDR *)&serverAddr, sizeof(serverAddr));

            // 等待外部DNS服务器响应（使用select实现超时）
            fd_set readSet;
            FD_ZERO(&readSet);
            FD_SET(serverSocket, &readSet);
            struct timeval timeout = {5, 0}; // 5秒超时
            if (select(0, &readSet, NULL, NULL, &timeout) <= 0)
            {
                continue; // 超时或错误，跳过此请求
            }

            // 接收外部DNS服务器的响应
            recvLen = recvfrom(serverSocket, response, BUFFER_SIZE, 0, NULL, NULL);
            if (recvLen < sizeof(DnsHeader))
            {
                continue; // 响应数据包太小
            }

            // 恢复原始ID和客户端地址
            header = (DnsHeader *)response;
            uint16_t responseId = ntohs(header->id);
            uint16_t originalId;
            SOCKADDR_IN originalClient;
            if (findOriginalId(responseId, &originalId, &originalClient) != 0)
            {
                continue; // ID映射查找失败
            }
            header->id = htons(originalId); // 恢复原始查询ID

            // 解析响应中的IP地址并加入缓存
            int offset = sizeof(DnsHeader);

            // 跳过问题部分
            while (offset < recvLen && response[offset] != 0)
            {
                offset += (unsigned char)response[offset] + 1;
            }
            offset += 5; // 跳过结束符和查询类型、类别

            // 解析答案部分
            int answerCount = ntohs(header->answer_count);
            char ip[16];
            for (int i = 0; i < answerCount && offset + 12 <= recvLen; i++)
            {
                // 跳过域名指针
                if ((unsigned char)response[offset] == 0xc0)
                {
                    offset += 2;
                }

                // 读取记录类型
                uint16_t type = ntohs(*(uint16_t *)(response + offset));
                offset += 4; // 跳过类型和类别

                // 读取TTL
                uint32_t ttl = ntohl(*(uint32_t *)(response + offset));
                offset += 4;

                // 读取数据长度
                uint16_t dataLen = ntohs(*(uint16_t *)(response + offset));
                offset += 2;

                // 如果是A记录（IPv4地址）
                if (type == 1 && dataLen == 4)
                {
                    // 提取IP地址
                    sprintf(ip, "%d.%d.%d.%d", (unsigned char)response[offset],
                            (unsigned char)response[offset + 1],
                            (unsigned char)response[offset + 2],
                            (unsigned char)response[offset + 3]);

                    // 加入缓存
                    insertCache(domainBuffer, ip, ttl);
                    logQuery(originalId, domainBuffer, ip, "External");
                }
                offset += dataLen;
            }

            // 将响应转发给原始客户端
            sendto(localSocket, response, recvLen, 0, (SOCKADDR *)&originalClient, sizeof(originalClient));
        }
    }

    // 程序结束时清理资源
    cleanup(localSocket, serverSocket);
    return 0;
}