cmake_minimum_required(VERSION 3.10)
project(dns_relay VERSION 1.0.0 LANGUAGES C)

# 设置C标准
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_STANDARD_REQUIRED ON)

# 设置编译选项
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -Wextra")

# 设置包含目录
include_directories(include)

# 通用源文件
set(COMMON_SOURCES
    src/main.c
    src/dns_core.c
    src/config.c
)

# 平台检测和设置
if(WIN32)
    message(STATUS "Building for Windows")
    set(PLATFORM_SOURCES
        platform/windows/platform_win.c
        platform/windows/network_win.c
    )
    set(PLATFORM_LIBS ws2_32)
    add_definitions(-DPLATFORM_WINDOWS)
    
elseif(APPLE)
    message(STATUS "Building for macOS")
    set(PLATFORM_SOURCES
        platform/linux/platform_linux.c
        platform/linux/network_linux.c
    )
    set(PLATFORM_LIBS "")
    add_definitions(-DPLATFORM_MACOS)
    
elseif(UNIX)
    message(STATUS "Building for Linux")
    set(PLATFORM_SOURCES
        platform/linux/platform_linux.c
        platform/linux/network_linux.c
    )
    set(PLATFORM_LIBS "")
    add_definitions(-DPLATFORM_LINUX)
    
else()
    message(FATAL_ERROR "Unsupported platform")
endif()

# 创建可执行文件
add_executable(${PROJECT_NAME} 
    ${COMMON_SOURCES} 
    ${PLATFORM_SOURCES}
)

# 链接库
target_link_libraries(${PROJECT_NAME} ${PLATFORM_LIBS})

# 设置输出目录
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 调试和发布配置
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(${PROJECT_NAME} PRIVATE DEBUG)
    message(STATUS "Building in Debug mode")
elseif(CMAKE_BUILD_TYPE STREQUAL "Release")
    target_compile_definitions(${PROJECT_NAME} PRIVATE NDEBUG)
    target_compile_options(${PROJECT_NAME} PRIVATE -O2)
    message(STATUS "Building in Release mode")
endif()

# 安装规则
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
)

# 创建配置文件示例
configure_file(
    ${CMAKE_SOURCE_DIR}/dnsrelay.txt.example
    ${CMAKE_BINARY_DIR}/bin/dnsrelay.txt
    COPYONLY
)

# 打印构建信息
message(STATUS "Project: ${PROJECT_NAME}")
message(STATUS "Version: ${PROJECT_VERSION}")
message(STATUS "C Compiler: ${CMAKE_C_COMPILER}")
message(STATUS "Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "Platform Libraries: ${PLATFORM_LIBS}")

# 自定义目标
add_custom_target(run
    COMMAND ${CMAKE_BINARY_DIR}/bin/${PROJECT_NAME}
    DEPENDS ${PROJECT_NAME}
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    COMMENT "Running DNS Relay Server"
)

# 清理目标
add_custom_target(clean-all
    COMMAND ${CMAKE_COMMAND} -E remove_directory ${CMAKE_BINARY_DIR}/bin
    COMMAND ${CMAKE_COMMAND} -E remove_directory ${CMAKE_BINARY_DIR}/CMakeFiles
    COMMENT "Cleaning all build files"
)
