# DNS Relay Server - 跨平台版本

一个支持 Windows、Linux 和 macOS 的高性能 DNS 中继服务器，具有本地域名解析、缓存机制和域名屏蔽功能。

## 功能特性

- ✅ **跨平台支持**: Windows、Linux、macOS
- ✅ **本地域名解析**: 支持自定义域名映射
- ✅ **DNS缓存**: 提高查询性能，减少外部DNS请求
- ✅ **域名屏蔽**: 支持屏蔽特定域名
- ✅ **并发处理**: 支持多客户端同时查询
- ✅ **详细日志**: 记录所有DNS查询活动
- ✅ **配置文件**: 灵活的域名映射配置

## 项目结构

```
dns_relay/
├── include/                 # 头文件
│   ├── platform.h          # 平台抽象层
│   ├── network_wrapper.h   # 网络操作封装
│   ├── dns_types.h         # DNS数据结构
│   └── common.h            # 通用定义
├── src/                    # 核心源代码
│   ├── main.c              # 主程序
│   ├── dns_core.c          # DNS核心逻辑
│   └── config.c            # 配置文件处理
├── platform/               # 平台特定实现
│   ├── windows/            # Windows实现
│   │   ├── platform_win.c
│   │   └── network_win.c
│   └── linux/              # Linux/macOS实现
│       ├── platform_linux.c
│       └── network_linux.c
├── build/                  # 构建输出目录
├── Makefile               # Make构建文件
├── CMakeLists.txt         # CMake构建文件
└── README.md              # 项目说明
```

## 编译和安装

### 方法1: 使用 Make

```bash
# 编译
make

# 编译调试版本
make debug

# 编译发布版本
make release

# 清理
make clean

# 运行
make run

# 查看帮助
make help
```

### 方法2: 使用 CMake

```bash
# 创建构建目录
mkdir build && cd build

# 配置项目
cmake ..

# 编译
cmake --build .

# 安装
cmake --install .

# 运行
./bin/dns_relay
```

### Windows 特殊说明

在 Windows 上，您可以使用以下工具之一：
- **MinGW-w64**: `mingw32-make` 或 `make`
- **MSYS2**: `make`
- **Visual Studio**: 使用 CMake 生成 VS 项目文件

## 配置文件

创建 `dnsrelay.txt` 配置文件，格式如下：

```
# DNS中继配置文件
# 格式: IP地址 域名

# 本地解析
************* local.example.com
******** internal.company.com

# 屏蔽域名 (使用0.0.0.0)
0.0.0.0 ads.example.com
0.0.0.0 malware.badsite.com

# 自定义DNS解析
******* google-dns.local
******* cloudflare-dns.local
```

## 使用方法

1. **启动服务器**:
   ```bash
   ./dns_relay
   ```

2. **配置客户端DNS**:
   将客户端的DNS服务器设置为运行DNS中继服务器的机器IP地址（默认端口53）

3. **查看日志**:
   服务器会实时显示DNS查询日志，包括查询来源（缓存、本地表、外部DNS）

## 查询优先级

1. **缓存查询**: 首先检查本地缓存
2. **本地表查询**: 检查配置文件中的域名映射
3. **外部DNS查询**: 转发到外部DNS服务器（默认*******）

## 平台差异处理

本项目通过抽象层设计实现跨平台兼容：

### 网络层差异
- **Windows**: 使用 Winsock2 API
- **Linux/macOS**: 使用标准 BSD Socket API

### 时间处理差异
- **Windows**: `GetLocalTime()` + `SYSTEMTIME`
- **Linux/macOS**: `gettimeofday()` + `struct tm`

### 系统调用差异
- **睡眠函数**: `Sleep()` vs `usleep()`
- **非阻塞设置**: `ioctlsocket()` vs `fcntl()`
- **字符串复制**: `_strdup()` vs `strdup()`

## 性能特性

- **非阻塞I/O**: 避免服务器阻塞
- **内存缓存**: TTL管理的DNS缓存
- **ID映射**: 支持并发查询处理
- **错误恢复**: 完善的错误处理机制

## 开发和调试

### 编译选项
- **调试版本**: 包含调试符号和详细日志
- **发布版本**: 优化性能，移除调试信息

### 日志级别
程序会输出详细的查询日志，格式：
```
[时:分:秒.毫秒] ID=查询ID, Domain=域名, IP=IP地址, Source=来源
```

## 许可证

本项目采用 MIT 许可证。

## 作者

Designed by Haokun Tian

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。
