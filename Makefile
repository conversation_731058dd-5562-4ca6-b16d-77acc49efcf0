# DNS Relay Server - Cross-platform Makefile

# 项目名称
PROJECT_NAME = dns_relay

# 编译器设置
CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -Iinclude

# 目录设置
SRC_DIR = src
INCLUDE_DIR = include
PLATFORM_DIR = platform
BUILD_DIR = build

# 检测操作系统
UNAME_S := $(shell uname -s 2>/dev/null || echo Windows)

# 根据操作系统设置平台相关变量
ifeq ($(UNAME_S),Linux)
    PLATFORM = linux
    PLATFORM_SRC = $(PLATFORM_DIR)/linux/platform_linux.c $(PLATFORM_DIR)/linux/network_linux.c
    LIBS = 
    EXECUTABLE = $(PROJECT_NAME)
    CFLAGS += -DPLATFORM_LINUX
endif

ifeq ($(UNAME_S),Darwin)
    PLATFORM = macos
    PLATFORM_SRC = $(PLATFORM_DIR)/linux/platform_linux.c $(PLATFORM_DIR)/linux/network_linux.c
    LIBS = 
    EXECUTABLE = $(PROJECT_NAME)
    CFLAGS += -DPLATFORM_MACOS
endif

# Windows (MinGW or MSYS2)
ifeq ($(OS),Windows_NT)
    PLATFORM = windows
    PLATFORM_SRC = $(PLATFORM_DIR)/windows/platform_win.c $(PLATFORM_DIR)/windows/network_win.c
    LIBS = -lws2_32
    EXECUTABLE = $(PROJECT_NAME).exe
    CFLAGS += -DPLATFORM_WINDOWS
endif

# 如果无法检测到平台，默认使用Linux
ifndef PLATFORM
    PLATFORM = linux
    PLATFORM_SRC = $(PLATFORM_DIR)/linux/platform_linux.c $(PLATFORM_DIR)/linux/network_linux.c
    LIBS = 
    EXECUTABLE = $(PROJECT_NAME)
    CFLAGS += -DPLATFORM_LINUX
endif

# 源文件
COMMON_SRC = $(SRC_DIR)/main.c $(SRC_DIR)/dns_core.c $(SRC_DIR)/config.c
ALL_SRC = $(COMMON_SRC) $(PLATFORM_SRC)

# 目标文件
COMMON_OBJ = $(COMMON_SRC:%.c=$(BUILD_DIR)/%.o)
PLATFORM_OBJ = $(PLATFORM_SRC:%.c=$(BUILD_DIR)/%.o)
ALL_OBJ = $(COMMON_OBJ) $(PLATFORM_OBJ)

# 默认目标
all: info $(BUILD_DIR)/$(EXECUTABLE)

# 显示构建信息
info:
	@echo "Building DNS Relay Server for $(PLATFORM)"
	@echo "Compiler: $(CC)"
	@echo "Flags: $(CFLAGS)"
	@echo "Libraries: $(LIBS)"
	@echo "Executable: $(EXECUTABLE)"
	@echo ""

# 主程序
$(BUILD_DIR)/$(EXECUTABLE): $(ALL_OBJ)
	@echo "Linking $(EXECUTABLE)..."
	@$(CC) $(ALL_OBJ) -o $@ $(LIBS)
	@echo "Build successful!"

# 编译通用源文件
$(BUILD_DIR)/$(SRC_DIR)/%.o: $(SRC_DIR)/%.c
	@mkdir -p $(dir $@)
	@echo "Compiling $<..."
	@$(CC) $(CFLAGS) -c $< -o $@

# 编译平台特定源文件
$(BUILD_DIR)/$(PLATFORM_DIR)/%.o: $(PLATFORM_DIR)/%.c
	@mkdir -p $(dir $@)
	@echo "Compiling $<..."
	@$(CC) $(CFLAGS) -c $< -o $@

# 清理
clean:
	@echo "Cleaning build files..."
	@rm -rf $(BUILD_DIR)
	@echo "Clean complete!"

# 安装（可选）
install: $(BUILD_DIR)/$(EXECUTABLE)
	@echo "Installing $(EXECUTABLE)..."
	@cp $(BUILD_DIR)/$(EXECUTABLE) /usr/local/bin/ 2>/dev/null || \
	 cp $(BUILD_DIR)/$(EXECUTABLE) . && echo "Copied to current directory"

# 运行
run: $(BUILD_DIR)/$(EXECUTABLE)
	@echo "Starting DNS Relay Server..."
	@cd $(BUILD_DIR) && ./$(EXECUTABLE)

# 调试版本
debug: CFLAGS += -g -DDEBUG
debug: $(BUILD_DIR)/$(EXECUTABLE)

# 发布版本
release: CFLAGS += -O2 -DNDEBUG
release: $(BUILD_DIR)/$(EXECUTABLE)

# 创建目录
$(BUILD_DIR):
	@mkdir -p $(BUILD_DIR)

# 帮助信息
help:
	@echo "DNS Relay Server - Cross-platform Build System"
	@echo ""
	@echo "Available targets:"
	@echo "  all      - Build the project (default)"
	@echo "  clean    - Remove build files"
	@echo "  debug    - Build debug version"
	@echo "  release  - Build optimized release version"
	@echo "  install  - Install the executable"
	@echo "  run      - Build and run the server"
	@echo "  help     - Show this help message"
	@echo ""
	@echo "Detected platform: $(PLATFORM)"

# 声明伪目标
.PHONY: all clean install run debug release help info

# 依赖关系
$(ALL_OBJ): | $(BUILD_DIR)
